#!/usr/bin/env python3
"""
木偶AI翻唱 Flet客户端启动脚本
"""

import sys
import subprocess
import os

def check_and_install_dependencies():
    """检查并安装依赖"""
    try:
        import flet
        import requests
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"缺少依赖: {e}")
        print("正在安装依赖...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "-r", "requirements_flet.txt"
            ])
            print("✓ 依赖安装完成")
            return True
        except subprocess.CalledProcessError:
            print("✗ 依赖安装失败")
            return False

def main():
    """主函数"""
    print("=" * 50)
    print("木偶AI翻唱 Flet客户端")
    print("=" * 50)
    
    # 检查依赖
    if not check_and_install_dependencies():
        print("请手动安装依赖: pip install -r requirements_flet.txt")
        input("按回车键退出...")
        return
    
    # 启动客户端
    try:
        print("正在启动客户端...")
        from flet_client import main
        import flet as ft
        
        ft.app(target=main, assets_dir="assets")
        
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()

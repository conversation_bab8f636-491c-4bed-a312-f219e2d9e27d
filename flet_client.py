"""
木偶AI翻唱 Flet 客户端
复刻原PyQt5界面，调用FastAPI后端服务
"""

import flet as ft
import os
import json
import time
import shutil
import subprocess
import threading
import requests
from pathlib import Path
from typing import Optional, Dict, List

# 配置文件路径
CONFIG_FILE = "client_config.json"
DEFAULT_API_URL = "http://0.0.0.0:8000"

class APIClient:
    """API客户端类"""

    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = 30

    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            return response.status_code == 200
        except:
            return False

    def upload_audio(self, file_path: str) -> Dict:
        """上传音频文件"""
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f, 'audio/wav')}
            response = self.session.post(f"{self.base_url}/upload_audio/", files=files)
            response.raise_for_status()
            return response.json()

    def get_models(self) -> List[str]:
        """获取模型列表"""
        response = self.session.get(f"{self.base_url}/models/")
        response.raise_for_status()
        return response.json().get('models', [])

    def get_configs(self) -> List[str]:
        """获取配置列表"""
        response = self.session.get(f"{self.base_url}/configs/")
        response.raise_for_status()
        return response.json().get('configs', [])

    def start_processing(self, params: Dict) -> str:
        """启动处理任务"""
        response = self.session.post(f"{self.base_url}/process/start/", json=params)
        response.raise_for_status()
        return response.json()['task_id']

    def get_task_status(self, task_id: str) -> Dict:
        """获取任务状态"""
        response = self.session.get(f"{self.base_url}/process/status/{task_id}")
        response.raise_for_status()
        return response.json()

    def stop_processing(self, task_id: str) -> Dict:
        """停止处理任务"""
        response = self.session.post(f"{self.base_url}/process/stop/", json={"task_id": task_id})
        response.raise_for_status()
        return response.json()

    def download_file(self, file_path: str, local_path: str):
        """下载文件"""
        response = self.session.get(f"{self.base_url}/download_file/", params={"file_path": file_path})
        response.raise_for_status()
        with open(local_path, 'wb') as f:
            f.write(response.content)

class ConfigManager:
    """配置管理器"""

    @staticmethod
    def load_config() -> Dict:
        """加载配置"""
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {"api_url": DEFAULT_API_URL}

    @staticmethod
    def save_config(config: Dict):
        """保存配置"""
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

class LocalAPIManager:
    """本地API管理器"""

    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.api_script_path = "main_api.py"

    def start_local_api(self) -> bool:
        """启动本地API服务"""
        if not os.path.exists(self.api_script_path):
            return False

        try:
            # 启动本地API服务
            self.process = subprocess.Popen(
                ["python", self.api_script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # 等待服务启动
            time.sleep(3)

            # 检查服务是否正常启动
            api_client = APIClient("http://localhost:8000")
            return api_client.health_check()

        except Exception as e:
            print(f"启动本地API失败: {e}")
            return False

    def stop_local_api(self):
        """停止本地API服务"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                if self.process:
                    self.process.kill()
            self.process = None

class MuOuAIClient:
    """木偶AI翻唱客户端主类"""

    def __init__(self, page: ft.Page):
        self.page = page
        self.config = ConfigManager.load_config()
        self.api_client = APIClient(self.config["api_url"])
        self.local_api_manager = LocalAPIManager()
        self.current_task_id: Optional[str] = None
        self.selected_audio_path: Optional[str] = None

        # 确保outputs目录存在
        os.makedirs("outputs", exist_ok=True)

        # 初始化UI组件
        self.init_ui_components()

        # 启动API连接检查
        self.check_api_connection()

    def init_ui_components(self):
        """初始化UI组件"""
        # 主题设置
        self.page.theme_mode = ft.ThemeMode.DARK
        self.page.title = "木偶AI翻唱"
        self.page.window_width = 1250
        self.page.window_height = 1000
        self.page.window_resizable = True

        # 颜色主题 - 参考原始PyQt5界面
        self.primary_color = "#58A6FF"  # 蓝色强调色
        self.secondary_color = "#21262D"  # 次级背景
        self.background_color = "#161B22"  # 主背景
        self.surface_color = "#21262D"  # 卡片背景
        self.border_color = "#30363D"  # 边框颜色
        self.text_color = "#E6EDF3"  # 主要文本
        self.secondary_text_color = "#8B949E"  # 次级文本

        # 创建UI组件
        self.create_left_panel()
        self.create_right_panel()

        # 布局
        self.setup_layout()

    def create_left_panel(self):
        """创建左侧面板 - 歌曲管理"""
        # Logo区域
        logo_container = ft.Container(
            content=ft.Image(
                src="assets/images/logo.png",
                width=120,
                height=120,
                fit=ft.ImageFit.CONTAIN
            ),
            alignment=ft.alignment.center,
            padding=ft.padding.only(top=20, bottom=20)
        )

        # API地址配置
        self.api_url_field = ft.TextField(
            label="API接口地址",
            value=self.config["api_url"],
            expand=True,
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            color=self.text_color,
            label_style=ft.TextStyle(color=self.secondary_text_color)
        )

        self.save_api_btn = ft.ElevatedButton(
            text="保存",
            on_click=self.save_api_config,
            bgcolor=self.primary_color,
            color=ft.Colors.WHITE,
            width=80
        )

        api_config_row = ft.Row([
            self.api_url_field,
            self.save_api_btn
        ], spacing=10)

        # 歌曲列表标题
        songs_header = ft.Row([
            ft.Text("歌曲管理", size=18, weight=ft.FontWeight.BOLD, color=self.text_color),
            ft.IconButton(
                icon=ft.Icons.REFRESH,
                tooltip="刷新歌曲列表",
                on_click=self.refresh_songs,
                icon_color=self.primary_color
            )
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)

        # 歌曲列表
        self.song_list = ft.ListView(
            expand=True,
            spacing=5,
            padding=10
        )

        # 初始提示
        self.no_songs_text = ft.Text(
            "暂无成品歌曲",
            color=self.secondary_text_color,
            text_align=ft.TextAlign.CENTER
        )

        # 左侧面板容器
        self.left_panel = ft.Container(
            content=ft.Column([
                logo_container,
                api_config_row,
                ft.Divider(color=self.border_color, height=1),
                songs_header,
                ft.Container(
                    content=self.song_list,
                    expand=True,
                    bgcolor=self.surface_color,
                    border_radius=8,
                    padding=10
                )
            ], spacing=15),
            bgcolor=self.surface_color,
            padding=15,
            border_radius=10,
            width=380,
            expand=False
        )

    def create_right_panel(self):
        """创建右侧面板 - 主要控制区域"""
        # 上传音频区域
        self.upload_area = ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.MUSIC_NOTE, size=50, color=self.primary_color),
                ft.Text("在此处选择或拖拽音频文件", color=self.secondary_text_color, text_align=ft.TextAlign.CENTER),
                ft.Text("支持WAV、MP3、OGG格式", size=12, color=self.secondary_text_color, text_align=ft.TextAlign.CENTER)
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10),
            bgcolor=self.surface_color,
            border=ft.border.all(2, self.primary_color),
            border_radius=10,
            padding=30,
            height=150,
            on_click=self.select_audio_file
        )

        # 处理模式和输出格式
        self.processing_mode = ft.Dropdown(
            label="处理模式",
            value="完整模式",
            options=[
                ft.dropdown.Option("完整模式"),
                ft.dropdown.Option("干声模式")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            color=self.text_color,
            width=150
        )

        self.output_format = ft.Dropdown(
            label="输出格式",
            value="WAV",
            options=[
                ft.dropdown.Option("WAV"),
                ft.dropdown.Option("MP3")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            color=self.text_color,
            width=150
        )

        upload_section = ft.Container(
            content=ft.Column([
                ft.Text("上传音频", size=18, weight=ft.FontWeight.BOLD, color=self.text_color),
                self.upload_area,
                ft.Row([
                    self.processing_mode,
                    self.output_format
                ], spacing=20)
            ], spacing=15),
            bgcolor=self.surface_color,
            padding=15,
            border_radius=10,
            margin=ft.margin.only(bottom=15)
        )

        # 翻唱分轨区域
        separation_section = self.create_separation_section()

        # 混响与和声区域
        reverb_section = self.create_reverb_section()

        # 高级参数配置区域
        advanced_section = self.create_advanced_section()

        # 控制按钮区域
        control_section = self.create_control_section()

        # 状态显示区域
        status_section = self.create_status_section()

        # 右侧面板滚动容器
        scroll_content = ft.Column([
            upload_section,
            separation_section,
            reverb_section,
            advanced_section,
            control_section,
            status_section
        ], spacing=15)

        self.right_panel = ft.Container(
            content=ft.Column([
                ft.Container(
                    content=scroll_content,
                    expand=True,
                    padding=10
                )
            ]),
            expand=True,
            bgcolor=self.background_color,
            padding=10
        )

    def create_upload_section(self):
        """创建上传区域"""
        self.upload_area = ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.MUSIC_NOTE, size=50, color=self.primary_color),
                ft.Text("在此处选择或拖拽音频文件", color=ft.Colors.WHITE70, text_align=ft.TextAlign.CENTER),
                ft.Text("支持WAV、MP3、OGG格式", size=12, color=ft.Colors.WHITE54, text_align=ft.TextAlign.CENTER)
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10),
            bgcolor=self.surface_color,
            border=ft.border.all(2, self.primary_color),
            border_radius=10,
            padding=30,
            height=150,
            on_click=self.select_audio_file
        )

        # 处理模式和输出格式
        self.processing_mode = ft.Dropdown(
            label="处理模式",
            value="完整模式",
            options=[
                ft.dropdown.Option("完整模式"),
                ft.dropdown.Option("干声模式")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            width=150
        )

        self.output_format = ft.Dropdown(
            label="输出格式",
            value="WAV",
            options=[
                ft.dropdown.Option("WAV"),
                ft.dropdown.Option("MP3")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            width=150
        )

        self.upload_section = ft.Container(
            content=ft.Column([
                ft.Text("上传音频", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE),
                self.upload_area,
                ft.Row([
                    self.processing_mode,
                    self.output_format
                ], spacing=20)
            ], spacing=15),
            bgcolor=self.surface_color,
            padding=15,
            border_radius=10
        )

    def create_separation_section(self):
        """创建翻唱分轨区域"""
        return ft.Container(
            content=ft.Column([
                ft.Text("翻唱分轨", size=18, weight=ft.FontWeight.BOLD, color=self.text_color),
                ft.Text("模型与音高", size=14, color=self.secondary_text_color),

                # 模型选择行
                ft.Row([
                    ft.Column([
                        ft.Text("音色模型(.pt):", color=self.secondary_text_color),
                        self.create_model_dropdown()
                    ], expand=True),
                    ft.Column([
                        ft.Text("配置文件(.yaml):", color=self.secondary_text_color),
                        self.create_config_dropdown()
                    ], expand=True)
                ], spacing=20),

                # 音高调整
                ft.Row([
                    ft.Column([
                        ft.Text("人声音高:", color=self.secondary_text_color),
                        self.create_pitch_slider("vocal", 0)
                    ], expand=True),
                    ft.Column([
                        ft.Text("伴奏音高:", color=self.secondary_text_color),
                        self.create_pitch_slider("instrumental", 0)
                    ], expand=True)
                ], spacing=20)
            ], spacing=15),
            bgcolor=self.surface_color,
            padding=15,
            border_radius=10
        )

    def create_model_dropdown(self):
        """创建模型下拉框"""
        self.model_dropdown = ft.Dropdown(
            label="选择模型",
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            options=[],
            on_change=self.on_model_change
        )
        return self.model_dropdown

    def create_config_dropdown(self):
        """创建配置下拉框"""
        self.config_dropdown = ft.Dropdown(
            label="选择配置",
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            options=[]
        )
        return self.config_dropdown

    def create_pitch_slider(self, slider_type: str, default_value: int):
        """创建音高滑块"""
        slider = ft.Slider(
            min=-18,
            max=18,
            value=default_value,
            divisions=36,
            label="{value}",
            active_color=self.primary_color,
            on_change=lambda e: self.update_pitch_display(slider_type, e.control.value)
        )

        value_text = ft.Text(str(default_value), color=self.text_color, width=30)

        if slider_type == "vocal":
            self.vocal_pitch_slider = slider
            self.vocal_pitch_text = value_text
        else:
            self.instrumental_pitch_slider = slider
            self.instrumental_pitch_text = value_text

        return ft.Row([slider, value_text], spacing=10)

    def create_reverb_section(self):
        """创建混响与和声区域"""
        # 混响开关
        self.reverb_switch = ft.Switch(
            label="启用混响",
            value=False,
            active_color=self.primary_color,
            on_change=self.on_reverb_toggle
        )

        self.harmony_switch = ft.Switch(
            label="和声加入伴奏",
            value=False,
            active_color=self.primary_color
        )

        # 混响参数滑块
        self.room_size_slider = ft.Slider(
            min=0.0,
            max=1.0,
            value=0.60,
            divisions=100,
            label="{value:.2f}",
            active_color=self.primary_color,
            disabled=True
        )
        self.room_size_text = ft.Text("0.60", color=self.text_color, width=50)

        self.damping_slider = ft.Slider(
            min=0.0,
            max=1.0,
            value=0.10,
            divisions=100,
            label="{value:.2f}",
            active_color=self.primary_color,
            disabled=True
        )
        self.damping_text = ft.Text("0.10", color=self.text_color, width=50)

        self.wet_level_slider = ft.Slider(
            min=0.0,
            max=1.0,
            value=0.30,
            divisions=100,
            label="{value:.2f}",
            active_color=self.primary_color,
            disabled=True
        )
        self.wet_level_text = ft.Text("0.30", color=self.text_color, width=50)

        self.dry_level_slider = ft.Slider(
            min=0.0,
            max=1.0,
            value=0.90,
            divisions=100,
            label="{value:.2f}",
            active_color=self.primary_color,
            disabled=True
        )
        self.dry_level_text = ft.Text("0.90", color=self.text_color, width=50)

        # 绑定滑块事件
        self.room_size_slider.on_change = lambda e: self.update_reverb_display("room_size", e.control.value)
        self.damping_slider.on_change = lambda e: self.update_reverb_display("damping", e.control.value)
        self.wet_level_slider.on_change = lambda e: self.update_reverb_display("wet_level", e.control.value)
        self.dry_level_slider.on_change = lambda e: self.update_reverb_display("dry_level", e.control.value)

        return ft.Container(
            content=ft.Column([
                ft.Text("混响与和声", size=18, weight=ft.FontWeight.BOLD, color=self.text_color),
                ft.Row([
                    self.reverb_switch,
                    self.harmony_switch
                ], spacing=30),

                # 混响参数
                ft.Row([
                    ft.Column([
                        ft.Text("房间大小:", color=self.secondary_text_color),
                        ft.Row([self.room_size_slider, self.room_size_text], spacing=10)
                    ], expand=True),
                    ft.Column([
                        ft.Text("阻尼:", color=self.secondary_text_color),
                        ft.Row([self.damping_slider, self.damping_text], spacing=10)
                    ], expand=True)
                ], spacing=20),

                ft.Row([
                    ft.Column([
                        ft.Text("湿润度:", color=self.secondary_text_color),
                        ft.Row([self.wet_level_slider, self.wet_level_text], spacing=10)
                    ], expand=True),
                    ft.Column([
                        ft.Text("干燥度:", color=self.secondary_text_color),
                        ft.Row([self.dry_level_slider, self.dry_level_text], spacing=10)
                    ], expand=True)
                ], spacing=20)
            ], spacing=15),
            bgcolor=self.surface_color,
            padding=15,
            border_radius=10
        )

    def create_advanced_section(self):
        """创建高级参数配置区域"""
        # 声码器
        self.vocoder_dropdown = ft.Dropdown(
            label="声码器",
            value="pc_nsf_hifigan_testing",
            options=[
                ft.dropdown.Option("pc_nsf_hifigan_testing"),
                ft.dropdown.Option("nsf_hifigan"),
                ft.dropdown.Option("nsf_snake_hifigan")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color
        )

        # F0提取器
        self.f0_extractor_dropdown = ft.Dropdown(
            label="F0提取器",
            value="rmvpe (默认)",
            options=[
                ft.dropdown.Option("rmvpe (默认)"),
                ft.dropdown.Option("harvest"),
                ft.dropdown.Option("crepe"),
                ft.dropdown.Option("pm")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color
        )

        # 共振峰偏移
        self.formant_shift_slider = ft.Slider(
            min=-6,
            max=6,
            value=0,
            divisions=12,
            label="{value}",
            active_color=self.primary_color,
            on_change=lambda e: self.update_formant_display(e.control.value)
        )
        self.formant_shift_text = ft.Text("0", color=self.text_color, width=30)

        # 采样步数
        self.infer_steps_field = ft.TextField(
            label="采样步数",
            value="50",
            width=100,
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            keyboard_type=ft.KeyboardType.NUMBER
        )

        # 采样器
        self.method_dropdown = ft.Dropdown(
            label="采样器",
            value="euler",
            options=[
                ft.dropdown.Option("euler"),
                ft.dropdown.Option("rk4"),
                ft.dropdown.Option("heun")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color,
            width=120
        )

        # 设备选择
        self.device_dropdown = ft.Dropdown(
            label="设备选择",
            value="CUDA (默认)",
            options=[
                ft.dropdown.Option("CUDA (默认)"),
                ft.dropdown.Option("CPU"),
                ft.dropdown.Option("MPS")
            ],
            bgcolor=self.surface_color,
            border_color=self.primary_color
        )

        return ft.Container(
            content=ft.Column([
                ft.Text("高级参数配置", size=18, weight=ft.FontWeight.BOLD, color=self.text_color),

                ft.Row([
                    ft.Column([
                        ft.Text("声码器:", color=self.secondary_text_color),
                        self.vocoder_dropdown
                    ], expand=True),
                    ft.Column([
                        ft.Text("F0提取器:", color=self.secondary_text_color),
                        self.f0_extractor_dropdown
                    ], expand=True)
                ], spacing=20),

                ft.Row([
                    ft.Column([
                        ft.Text("共振峰偏移:", color=self.secondary_text_color),
                        ft.Row([self.formant_shift_slider, self.formant_shift_text], spacing=10)
                    ], expand=True),
                    ft.Column([
                        ft.Text("采样步数:", color=self.secondary_text_color),
                        self.infer_steps_field
                    ], expand=True)
                ], spacing=20),

                ft.Row([
                    ft.Column([
                        ft.Text("采样器:", color=self.secondary_text_color),
                        self.method_dropdown
                    ], expand=True),
                    ft.Column([
                        ft.Text("设备选择:", color=self.secondary_text_color),
                        self.device_dropdown
                    ], expand=True)
                ], spacing=20)
            ], spacing=15),
            bgcolor=self.surface_color,
            padding=15,
            border_radius=10
        )

    def create_control_section(self):
        """创建控制区域"""
        self.start_btn = ft.ElevatedButton(
            text="🎵 一键翻唱",
            icon=ft.Icons.PLAY_ARROW,
            on_click=self.start_processing,
            bgcolor=self.primary_color,
            color=ft.Colors.WHITE,
            width=200,
            height=50,
            style=ft.ButtonStyle(
                text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD)
            )
        )

        self.stop_btn = ft.ElevatedButton(
            text="停止",
            icon=ft.Icons.STOP,
            on_click=self.stop_processing,
            bgcolor=ft.Colors.RED_400,
            color=ft.Colors.WHITE,
            width=100,
            height=50,
            disabled=True
        )

        return ft.Container(
            content=ft.Row([
                self.start_btn,
                self.stop_btn
            ], spacing=20, alignment=ft.MainAxisAlignment.CENTER),
            bgcolor=self.surface_color,
            padding=20,
            border_radius=10
        )

    def create_status_section(self):
        """创建状态区域"""
        self.status_text = ft.Text(
            "状态: 空闲",
            size=16,
            color=self.text_color
        )

        self.progress_bar = ft.ProgressBar(
            width=400,
            color=self.primary_color,
            bgcolor=self.secondary_color,
            value=0
        )

        self.progress_text = ft.Text(
            "0%",
            size=14,
            color=self.secondary_text_color
        )

        return ft.Container(
            content=ft.Column([
                ft.Text("处理状态", size=18, weight=ft.FontWeight.BOLD, color=self.text_color),
                self.status_text,
                ft.Row([
                    self.progress_bar,
                    self.progress_text
                ], spacing=10, alignment=ft.MainAxisAlignment.CENTER)
            ], spacing=10),
            bgcolor=self.surface_color,
            padding=15,
            border_radius=10
        )

    def setup_layout(self):
        """设置布局"""
        # 主布局 - 左右分栏
        main_content = ft.Row([
            self.left_panel,
            self.right_panel
        ], spacing=20, expand=True)

        # 页面内容
        self.page.add(
            ft.Container(
                content=main_content,
                padding=20,
                bgcolor=self.background_color,
                expand=True
            )
        )

    # ==================== 事件处理方法 ====================

    def update_pitch_display(self, slider_type: str, value: float):
        """更新音高显示"""
        int_value = int(value)
        if slider_type == "vocal":
            self.vocal_pitch_text.value = str(int_value)
        else:
            self.instrumental_pitch_text.value = str(int_value)
        self.page.update()

    def update_reverb_display(self, param_type: str, value: float):
        """更新混响参数显示"""
        formatted_value = f"{value:.2f}"
        if param_type == "room_size":
            self.room_size_text.value = formatted_value
        elif param_type == "damping":
            self.damping_text.value = formatted_value
        elif param_type == "wet_level":
            self.wet_level_text.value = formatted_value
        elif param_type == "dry_level":
            self.dry_level_text.value = formatted_value
        self.page.update()

    def update_formant_display(self, value: float):
        """更新共振峰显示"""
        self.formant_shift_text.value = str(int(value))
        self.page.update()

    def on_reverb_toggle(self, e):
        """混响开关切换"""
        enabled = e.control.value
        self.room_size_slider.disabled = not enabled
        self.damping_slider.disabled = not enabled
        self.wet_level_slider.disabled = not enabled
        self.dry_level_slider.disabled = not enabled
        self.page.update()

    def on_model_change(self, e):
        """模型选择变化"""
        selected_model = e.control.value
        if selected_model:
            # 自动匹配配置文件
            model_name = selected_model.replace('.pt', '')
            matching_config = f"{model_name}.yaml"

            # 检查是否有匹配的配置文件
            for option in self.config_dropdown.options:
                if option.key == matching_config:
                    self.config_dropdown.value = matching_config
                    break

            self.page.update()

    def save_api_config(self, e):
        """保存API配置"""
        new_url = self.api_url_field.value.strip()
        if new_url:
            self.config["api_url"] = new_url
            ConfigManager.save_config(self.config)
            self.api_client = APIClient(new_url)

            # 显示保存成功消息
            self.show_snackbar("API地址已保存", ft.Colors.GREEN)

            # 重新检查连接
            self.check_api_connection()

    def show_snackbar(self, message: str, color: str = None):
        """显示提示消息"""
        snackbar = ft.SnackBar(
            content=ft.Text(message, color=ft.Colors.WHITE),
            bgcolor=color or self.primary_color
        )
        self.page.snack_bar = snackbar
        snackbar.open = True
        self.page.update()

    def select_audio_file(self, e):
        """选择音频文件"""
        def file_picker_result(e: ft.FilePickerResultEvent):
            if e.files:
                file_path = e.files[0].path
                self.selected_audio_path = file_path

                # 更新上传区域显示
                filename = os.path.basename(file_path)
                self.upload_area.content = ft.Column([
                    ft.Icon(ft.Icons.AUDIO_FILE, size=50, color=self.primary_color),
                    ft.Text(f"已选择: {filename}", color=ft.Colors.WHITE, text_align=ft.TextAlign.CENTER),
                    ft.Text("点击重新选择", size=12, color=ft.Colors.WHITE54, text_align=ft.TextAlign.CENTER)
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=10)
                self.page.update()

        file_picker = ft.FilePicker(on_result=file_picker_result)
        self.page.overlay.append(file_picker)
        self.page.update()

        file_picker.pick_files(
            dialog_title="选择音频文件",
            file_type=ft.FilePickerFileType.AUDIO,
            allow_multiple=False
        )

    def check_api_connection(self):
        """检查API连接"""
        def check_connection():
            try:
                # 尝试启动本地API
                if self.local_api_manager.start_local_api():
                    self.api_client = APIClient("http://localhost:8000")
                    self.update_connection_status("本地API已启动", ft.Colors.GREEN)
                    self.load_models_and_configs()
                    return

                # 检查远程API连接
                if self.api_client.health_check():
                    self.update_connection_status("API连接正常", ft.Colors.GREEN)
                    self.load_models_and_configs()
                else:
                    self.update_connection_status("API连接失败", ft.Colors.RED)

            except Exception as e:
                self.update_connection_status(f"连接错误: {str(e)}", ft.Colors.RED)

        # 在后台线程中检查连接
        threading.Thread(target=check_connection, daemon=True).start()

    def update_connection_status(self, message: str, color: str):
        """更新连接状态"""
        self.status_text.value = f"状态: {message}"
        self.status_text.color = color
        self.page.update()

    def load_models_and_configs(self):
        """加载模型和配置列表"""
        def load_data():
            try:
                # 加载模型列表
                models = self.api_client.get_models()
                self.model_dropdown.options = [ft.dropdown.Option(model) for model in models]
                if models:
                    self.model_dropdown.value = models[0]

                # 加载配置列表
                configs = self.api_client.get_configs()
                self.config_dropdown.options = [ft.dropdown.Option(config) for config in configs]
                if configs:
                    self.config_dropdown.value = configs[0]

                self.page.update()

            except Exception as e:
                self.show_snackbar(f"加载模型配置失败: {str(e)}", ft.Colors.RED)

        threading.Thread(target=load_data, daemon=True).start()

    def refresh_songs(self, e):
        """刷新歌曲列表"""
        self.load_song_list()

    def load_song_list(self):
        """加载歌曲列表"""
        self.song_list.controls.clear()

        outputs_dir = Path("outputs")
        if not outputs_dir.exists():
            return

        for song_dir in outputs_dir.iterdir():
            if song_dir.is_dir():
                song_item = self.create_song_item(song_dir.name, song_dir)
                self.song_list.controls.append(song_item)

        if not self.song_list.controls:
            self.song_list.controls.append(
                ft.Text("暂无成品歌曲", color=ft.Colors.WHITE70, text_align=ft.TextAlign.CENTER)
            )

        self.page.update()

    def create_song_item(self, song_name: str, song_path: Path):
        """创建歌曲项目"""
        def play_song(e):
            # 查找音频文件
            audio_files = list(song_path.glob("*.wav")) + list(song_path.glob("*.mp3"))
            if audio_files:
                os.startfile(str(audio_files[0]))  # Windows

        def open_folder(e):
            os.startfile(str(song_path))  # Windows

        def delete_song(e):
            def confirm_delete(e):
                if e.control.text == "确认删除":
                    try:
                        shutil.rmtree(song_path)
                        self.load_song_list()
                        self.show_snackbar(f"已删除歌曲: {song_name}", ft.colors.GREEN)
                    except Exception as ex:
                        self.show_snackbar(f"删除失败: {str(ex)}", ft.colors.RED)
                dialog.open = False
                self.page.update()

            dialog = ft.AlertDialog(
                title=ft.Text("确认删除"),
                content=ft.Text(f"确定要删除歌曲 '{song_name}' 及其所有文件吗？"),
                actions=[
                    ft.TextButton("取消", on_click=lambda e: setattr(dialog, 'open', False) or self.page.update()),
                    ft.TextButton("确认删除", on_click=confirm_delete)
                ]
            )
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()

        return ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.MUSIC_NOTE, color=self.primary_color),
                ft.Text(song_name, expand=True, color=ft.Colors.WHITE),
                ft.IconButton(ft.Icons.PLAY_ARROW, tooltip="播放", on_click=play_song),
                ft.IconButton(ft.Icons.FOLDER_OPEN, tooltip="打开文件夹", on_click=open_folder),
                ft.IconButton(ft.Icons.DELETE, tooltip="删除", on_click=delete_song, icon_color=ft.Colors.RED)
            ], spacing=5),
            bgcolor=ft.Colors.GREY_800,
            padding=10,
            border_radius=5,
            margin=ft.margin.only(bottom=5)
        )

    def start_processing(self, e):
        """开始处理"""
        if not self.selected_audio_path:
            self.show_snackbar("请先选择音频文件", ft.Colors.RED)
            return

        if not self.model_dropdown.value or not self.config_dropdown.value:
            self.show_snackbar("请选择模型和配置文件", ft.Colors.RED)
            return

        # 准备参数
        params = {
            "model_name": self.model_dropdown.value,
            "config_name": self.config_dropdown.value,
            "vocal_pitch": int(self.vocal_pitch_slider.value),
            "instrumental_pitch": int(self.instrumental_pitch_slider.value),
            "processing_mode": self.processing_mode.value,
            "output_format": self.output_format.value.lower(),
            "vocoder": self.vocoder_dropdown.value,
            "f0_extractor": self.f0_extractor_dropdown.value.split()[0],  # 去掉"(默认)"
            "formant_shift": int(self.formant_shift_slider.value),
            "infer_steps": int(self.infer_steps_field.value),
            "method": self.method_dropdown.value,
            "device": self.device_dropdown.value.split()[0].lower(),  # 去掉"(默认)"
            "enable_reverb": self.reverb_switch.value,
            "harmony_to_instrumental": self.harmony_switch.value
        }

        # 添加混响参数
        if self.reverb_switch.value:
            params.update({
                "room_size": self.room_size_slider.value,
                "damping": self.damping_slider.value,
                "wet_level": self.wet_level_slider.value,
                "dry_level": self.dry_level_slider.value
            })

        # 开始处理
        def process_audio():
            try:
                # 上传音频文件
                self.update_status("正在上传音频文件...", 10)
                self.api_client.upload_audio(self.selected_audio_path)

                # 启动处理任务
                self.update_status("正在启动处理任务...", 20)
                self.current_task_id = self.api_client.start_processing(params)

                # 启用停止按钮
                self.start_btn.disabled = True
                self.stop_btn.disabled = False
                self.page.update()

                # 监控处理进度
                self.monitor_processing()

            except Exception as ex:
                self.update_status(f"处理失败: {str(ex)}", 0)
                self.reset_controls()
                self.show_snackbar(f"处理失败: {str(ex)}", ft.Colors.RED)

        threading.Thread(target=process_audio, daemon=True).start()

    def stop_processing(self, e):
        """停止处理"""
        if self.current_task_id:
            try:
                self.api_client.stop_processing(self.current_task_id)
                self.update_status("已停止处理", 0)
                self.reset_controls()
                self.show_snackbar("已停止处理", ft.Colors.ORANGE)
            except Exception as ex:
                self.show_snackbar(f"停止失败: {str(ex)}", ft.Colors.RED)

    def monitor_processing(self):
        """监控处理进度"""
        def check_progress():
            while self.current_task_id:
                try:
                    status = self.api_client.get_task_status(self.current_task_id)

                    if status['status'] == 'completed':
                        self.update_status("处理完成，正在下载文件...", 90)
                        self.download_results(status.get('output_files', []))
                        break
                    elif status['status'] == 'failed':
                        self.update_status(f"处理失败: {status.get('error', '未知错误')}", 0)
                        self.reset_controls()
                        break
                    elif status['status'] == 'stopped':
                        self.update_status("处理已停止", 0)
                        self.reset_controls()
                        break
                    else:
                        # 更新进度
                        progress = status.get('progress', 0)
                        self.update_status(f"正在处理: {status.get('current_step', '未知步骤')}", progress)

                    time.sleep(2)  # 每2秒检查一次

                except Exception as e:
                    self.update_status(f"监控错误: {str(e)}", 0)
                    self.reset_controls()
                    break

        threading.Thread(target=check_progress, daemon=True).start()

    def download_results(self, output_files: List[str]):
        """下载处理结果"""
        try:
            # 创建输出目录
            song_name = os.path.splitext(os.path.basename(self.selected_audio_path))[0]
            output_dir = Path("outputs") / song_name
            output_dir.mkdir(parents=True, exist_ok=True)

            # 下载所有文件
            for file_path in output_files:
                filename = os.path.basename(file_path)
                local_path = output_dir / filename
                self.api_client.download_file(file_path, str(local_path))

            self.update_status("处理完成！", 100)
            self.reset_controls()
            self.load_song_list()  # 刷新歌曲列表
            self.show_snackbar(f"处理完成！文件已保存到: {output_dir}", ft.Colors.GREEN)

        except Exception as e:
            self.update_status(f"下载失败: {str(e)}", 0)
            self.reset_controls()
            self.show_snackbar(f"下载失败: {str(e)}", ft.Colors.RED)

    def update_status(self, message: str, progress: int):
        """更新状态和进度"""
        self.status_text.value = f"状态: {message}"
        self.progress_bar.value = progress / 100.0
        self.progress_text.value = f"{progress}%"
        self.page.update()

    def reset_controls(self):
        """重置控制按钮"""
        self.start_btn.disabled = False
        self.stop_btn.disabled = True
        self.current_task_id = None
        self.page.update()

    def on_page_close(self, e):
        """页面关闭时清理资源"""
        self.local_api_manager.stop_local_api()


def main(page: ft.Page):
    """主函数"""
    app = MuOuAIClient(page)
    page.on_window_event = app.on_page_close
    app.load_song_list()  # 初始加载歌曲列表


if __name__ == "__main__":
    ft.app(target=main, assets_dir="assets")
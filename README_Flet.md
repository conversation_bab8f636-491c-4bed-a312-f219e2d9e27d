# 木偶AI翻唱 Flet客户端

这是一个使用Flet框架开发的木偶AI翻唱客户端，完全复刻了原PyQt5界面的布局和功能。

## 功能特点

### 🎵 核心功能
- **完整界面复刻**: 与原PyQt5版本保持一致的界面布局
- **API调用**: 通过FastAPI后端服务完成AI翻唱功能
- **本地文件管理**: 客户端本地管理成品歌曲文件
- **自动API启动**: 自动检测并启动本地API服务

### 🎛️ 界面功能
- **歌曲管理**: 
  - 显示本地outputs文件夹中的成品歌曲
  - 播放、打开文件夹、删除歌曲功能
  - API接口地址配置和保存
- **音频上传**: 支持WAV、MP3、OGG格式音频文件
- **模型配置**: 
  - 音色模型(.pt)和配置文件(.yaml)选择
  - 人声和伴奏音高调整
- **混响与和声**: 
  - 混响参数调整(房间大小、阻尼、湿润度、干燥度)
  - 和声加入伴奏选项
- **高级参数**: 
  - 声码器、F0提取器选择
  - 共振峰偏移、采样步数、采样器配置
  - 设备选择(CUDA/CPU/MPS)

### 🔧 技术特性
- **自动API管理**: 
  - 优先启动本地main_api.py脚本
  - 本地API不可用时连接远程API
  - 健康检查和连接状态监控
- **文件管理**: 
  - 按歌曲名称在outputs文件夹中创建子文件夹
  - 自动下载和组织处理结果文件
- **实时监控**: 
  - 处理进度实时显示
  - 任务状态监控和错误处理
- **配置持久化**: API地址配置自动保存

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements_flet.txt
```

### 2. 启动客户端
```bash
python run_flet_client.py
```
或直接运行：
```bash
python flet_client.py
```

### 3. 配置API地址
- 默认API地址: `http://0.0.0.0:8000`
- 可在歌曲管理区域修改API地址并保存
- 支持本地和远程API服务

## 文件结构

```
├── flet_client.py          # 主客户端程序
├── run_flet_client.py      # 启动脚本
├── requirements_flet.txt   # 依赖列表
├── client_config.json      # 客户端配置文件(自动生成)
├── outputs/                # 成品歌曲输出目录
│   └── [歌曲名称]/         # 按歌曲名称分类的文件夹
└── main_api.py            # 本地API服务(可选)
```

## API接口要求

客户端需要连接到兼容的FastAPI后端服务，API应提供以下端点：

- `GET /health` - 健康检查
- `POST /upload_audio/` - 上传音频文件
- `GET /models/` - 获取模型列表
- `GET /configs/` - 获取配置列表
- `POST /process/start/` - 启动处理任务
- `GET /process/status/{task_id}` - 获取任务状态
- `POST /process/stop/` - 停止处理任务
- `GET /download_file/` - 下载文件

## 使用说明

### 基本流程
1. **启动客户端**: 运行启动脚本或直接运行主程序
2. **检查连接**: 客户端会自动检查API连接状态
3. **选择音频**: 点击上传区域选择要处理的音频文件
4. **配置参数**: 选择模型、调整音高和其他参数
5. **开始处理**: 点击"一键翻唱"按钮开始处理
6. **监控进度**: 实时查看处理进度和状态
7. **管理结果**: 在歌曲管理区域播放、查看或删除成品

### 高级功能
- **混响效果**: 启用混响开关并调整相关参数
- **设备选择**: 根据硬件配置选择合适的计算设备
- **批量管理**: 在outputs文件夹中管理多个成品歌曲

## 注意事项

1. **API服务**: 确保有可用的FastAPI后端服务
2. **文件权限**: 确保程序有读写outputs文件夹的权限
3. **网络连接**: 使用远程API时需要稳定的网络连接
4. **硬件要求**: AI处理需要足够的计算资源

## 故障排除

### 常见问题
1. **API连接失败**: 检查API地址是否正确，服务是否运行
2. **文件上传失败**: 检查音频文件格式和大小
3. **处理失败**: 查看错误信息，检查模型和配置文件
4. **下载失败**: 检查网络连接和本地存储空间

### 日志和调试
- 客户端会显示详细的状态信息和错误消息
- 检查API服务的日志获取更多调试信息

## 开发信息

- **框架**: Flet (基于Flutter)
- **语言**: Python 3.7+
- **依赖**: flet, requests, pathlib2
- **兼容性**: Windows, macOS, Linux

## 更新日志

### v1.0.0
- 完整复刻PyQt5界面布局
- 实现所有核心功能
- 支持本地和远程API
- 完善的文件管理系统

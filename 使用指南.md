# 木偶AI翻唱 Flet客户端使用指南

## 快速开始

### 1. 启动客户端
- **方法一**: 双击 `启动Flet客户端.bat` (Windows)
- **方法二**: 运行 `python flet_client.py`
- **方法三**: 运行 `python run_flet_client.py`

### 2. 检查API连接
客户端启动后会自动：
1. 尝试启动本地 `main_api.py` 服务
2. 如果本地服务不可用，连接到配置的远程API
3. 在状态栏显示连接结果

### 3. 配置API地址（如需要）
1. 在左侧"歌曲管理"区域找到"API接口地址"输入框
2. 输入你的API服务地址（默认: http://0.0.0.0:8000）
3. 点击"保存"按钮

## 基本操作流程

### 步骤1: 选择音频文件
1. 点击右侧"上传音频"区域的虚线框
2. 在文件选择器中选择音频文件（支持WAV、MP3、OGG）
3. 选择处理模式和输出格式

### 步骤2: 配置模型参数
1. **模型选择**:
   - 选择音色模型(.pt文件)
   - 选择对应的配置文件(.yaml文件)

2. **音高调整**:
   - 调整人声音高（-18到+18半音）
   - 调整伴奏音高（-18到+18半音）

### 步骤3: 配置混响效果（可选）
1. 启用"启用混响"开关
2. 调整混响参数：
   - 房间大小 (0.0-1.0)
   - 阻尼 (0.0-1.0)
   - 湿润度 (0.0-1.0)
   - 干燥度 (0.0-1.0)
3. 选择是否"和声加入伴奏"

### 步骤4: 高级参数配置（可选）
1. **声码器**: 选择音频生成器类型
2. **F0提取器**: 选择音高提取方法
3. **共振峰偏移**: 调整音色特征
4. **采样参数**: 设置采样步数和采样器
5. **设备选择**: 选择计算设备（CUDA/CPU/MPS）

### 步骤5: 开始处理
1. 点击"🎵 一键翻唱"按钮
2. 观察状态栏的处理进度
3. 等待处理完成

### 步骤6: 管理成品
处理完成后，在左侧"歌曲管理"区域可以：
- **播放**: 点击播放按钮试听成品
- **打开文件夹**: 查看所有生成的文件
- **删除**: 删除不需要的成品

## 界面说明

### 左侧 - 歌曲管理
- **API配置**: 设置和保存API服务地址
- **成品列表**: 显示所有已完成的翻唱作品
- **操作按钮**: 播放、打开文件夹、删除

### 右侧 - 处理配置
- **上传音频**: 选择要处理的音频文件
- **翻唱分轨**: 模型选择和音高调整
- **混响与和声**: 音效参数配置
- **高级参数**: 详细的处理参数
- **控制按钮**: 开始/停止处理
- **状态显示**: 实时进度和状态信息

## 文件管理

### 输出目录结构
```
outputs/
├── 歌曲1/
│   ├── 歌曲1.wav          # 最终成品
│   ├── 歌曲1_vocal.wav    # 人声轨道
│   ├── 歌曲1_instrumental.wav  # 伴奏轨道
│   └── info.json          # 处理信息
└── 歌曲2/
    └── ...
```

### 配置文件
- `client_config.json`: 客户端配置（自动生成）
- `requirements_flet.txt`: Python依赖列表

## 常见问题

### Q: API连接失败怎么办？
A: 
1. 检查API地址是否正确
2. 确认API服务是否正在运行
3. 检查网络连接
4. 查看防火墙设置

### Q: 模型列表为空怎么办？
A:
1. 确认API连接正常
2. 检查API服务器上是否有模型文件
3. 重启客户端重新加载

### Q: 处理失败怎么办？
A:
1. 检查音频文件格式是否支持
2. 确认选择了正确的模型和配置
3. 查看错误信息提示
4. 检查服务器资源是否充足

### Q: 下载失败怎么办？
A:
1. 检查本地磁盘空间
2. 确认有写入权限
3. 检查网络连接稳定性

### Q: 如何更换API服务器？
A:
1. 在API接口地址输入框中输入新地址
2. 点击保存按钮
3. 客户端会自动重新连接

## 技术支持

### 日志信息
- 客户端会在状态栏显示详细的操作信息
- 错误信息会以红色提示显示
- 成功操作会以绿色提示显示

### 性能优化
- 使用CUDA设备可以显著提升处理速度
- 较低的采样步数可以加快处理但可能影响质量
- 关闭混响可以减少处理时间

### 兼容性
- 支持Windows、macOS、Linux
- 需要Python 3.7或更高版本
- 建议使用最新版本的Flet框架

## 更新和维护

### 更新依赖
```bash
pip install -r requirements_flet.txt --upgrade
```

### 清理缓存
删除 `client_config.json` 文件可以重置所有配置到默认值。

### 备份数据
定期备份 `outputs` 文件夹中的成品歌曲。
